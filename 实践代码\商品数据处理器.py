"""
商品数据处理器 - 监听网络请求并处理商品数据
功能：
1. 新建标签页访问指定网站
2. 监听特定API请求获取认证信息
3. 查询待处理商品总数并计算页数
4. 分页获取商品数据并提取关键信息
"""

from DrissionPage import ChromiumPage
import json
import math
import time


class ProductDataProcessor:
    def __init__(self):
        """初始化商品数据处理器"""
        # 创建浏览器页面对象，指定端口为9222
        self.page = ChromiumPage(port=9222)
        self.auth_headers = {}  # 存储认证头信息
        self.cookies = {}  # 存储cookies信息
        print("✅ 商品数据处理器初始化完成")
    
    def start_listening(self):
        """开始监听网络请求"""
        print("🔍 开始监听网络请求...")
        
        # 启动网络监听
        self.page.listen.start('https://agentseller.temu.com/bg/detroit/api/infoTicket/searchTicket')
        print("📡 已开始监听认证API: https://agentseller.temu.com/bg/detroit/api/infoTicket/searchTicket")
    
    def open_target_page(self):
        """打开目标页面"""
        target_url = "https://agentseller.temu.com/newon/product-select"
        print(f"🌐 正在打开新标签页: {target_url}")
        
        # 新建标签页并访问目标网站
        new_tab = self.page.new_tab(target_url)
        print("✅ 新标签页已打开并访问目标网站")
        return new_tab
    
    def capture_auth_info(self, timeout=30):
        """捕获认证信息"""
        print(f"⏳ 等待捕获认证信息（超时时间：{timeout}秒）...")
        
        try:
            # 等待监听到目标请求
            packet = self.page.listen.wait(timeout=timeout)
            
            if packet:
                print("✅ 成功捕获到认证请求！")
                
                # 提取请求头信息
                headers = packet.request.headers
                self.auth_headers = {
                    'User-Agent': headers.get('User-Agent', ''),
                    'Authorization': headers.get('Authorization', ''),
                    'Cookie': headers.get('Cookie', ''),
                    'Referer': headers.get('Referer', ''),
                    'X-Requested-With': headers.get('X-Requested-With', ''),
                }
                
                print("📋 认证信息已提取:")
                for key, value in self.auth_headers.items():
                    if value:
                        # 只显示前50个字符，避免输出过长
                        display_value = value[:50] + "..." if len(value) > 50 else value
                        print(f"   {key}: {display_value}")
                
                return True
            else:
                print("❌ 未能在指定时间内捕获到认证请求")
                return False
                
        except Exception as e:
            print(f"❌ 捕获认证信息时发生错误: {str(e)}")
            return False
    
    def query_todo_count(self):
        """查询待处理商品总数"""
        print("\n📊 正在查询待处理商品总数...")
        
        api_url = "https://agentseller.temu.com/api/kiana/mms/robin/querySupplierTodoCount"
        
        try:
            # 发送POST请求查询总数
            response = self.page.post(api_url, json={}, headers=self.auth_headers)
            
            if response and response.json:
                result = response.json
                print(f"📈 API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('success') and result.get('result'):
                    total_count = result['result'].get('total', 0)
                    print(f"🔢 待处理商品总数: {total_count}")
                    
                    # 计算总页数（每页50条）
                    page_size = 50
                    total_pages = math.ceil(total_count / page_size)
                    print(f"📄 每页{page_size}条，共需处理{total_pages}页")
                    
                    return total_count, total_pages
                else:
                    print("❌ 查询失败，响应数据异常")
                    return 0, 0
            else:
                print("❌ 请求失败，未获取到响应数据")
                return 0, 0
                
        except Exception as e:
            print(f"❌ 查询待处理商品总数时发生错误: {str(e)}")
            return 0, 0
    
    def fetch_product_data(self, page_num=1):
        """获取指定页的商品数据"""
        print(f"\n📦 正在获取第{page_num}页商品数据...")
        
        api_url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForChainSupplier"
        form_data = {
            "pageSize": 50,
            "pageNum": page_num,
            "supplierTodoTypeList": [6, 1]
        }
        
        try:
            # 发送POST请求获取商品数据
            response = self.page.post(api_url, json=form_data, headers=self.auth_headers)
            
            if response and response.json:
                result = response.json
                print(f"📋 第{page_num}页API响应成功")
                
                if result.get('success') and result.get('result'):
                    return result['result']
                else:
                    print(f"❌ 第{page_num}页数据获取失败")
                    return None
            else:
                print(f"❌ 第{page_num}页请求失败")
                return None
                
        except Exception as e:
            print(f"❌ 获取第{page_num}页商品数据时发生错误: {str(e)}")
            return None
    
    def extract_product_info(self, page_data, page_num):
        """提取商品关键信息"""
        print(f"\n🔍 正在提取第{page_num}页商品信息...")
        
        if not page_data or 'dataList' not in page_data:
            print("❌ 页面数据为空或格式异常")
            return
        
        data_list = page_data['dataList']
        print(f"📊 第{page_num}页共有{len(data_list)}条商品记录")
        
        for i, item in enumerate(data_list):
            try:
                print(f"\n--- 第{page_num}页第{i+1}条商品信息 ---")
                
                # 提取skcList
                skc_list = item.get('skcList', [])
                if not skc_list:
                    print("⚠️  该商品无skcList数据")
                    continue
                
                for j, skc_item in enumerate(skc_list):
                    print(f"  SKC项目 {j+1}:")
                    
                    # 提取skcId
                    skc_id = skc_item.get('skcId', '')
                    print(f"    skcId: {skc_id}")
                    
                    # 提取skuList中的skuId
                    sku_list = skc_item.get('skuList', [])
                    for k, sku_item in enumerate(sku_list):
                        sku_id = sku_item.get('skuId', '')
                        print(f"    skuId[{k}]: {sku_id}")
                    
                    # 提取supplierPriceReviewInfoList中的信息
                    price_review_list = skc_item.get('supplierPriceReviewInfoList', [])
                    for l, price_item in enumerate(price_review_list):
                        # 提取orderId
                        order_id = price_item.get('priceOrderId', '')
                        print(f"    orderId[{l}]: {order_id}")
                        
                        # 提取属性集
                        product_sku_list = price_item.get('productSkuList', [])
                        for m, product_sku in enumerate(product_sku_list):
                            property_list = product_sku.get('productPropertyList', [])
                            for n, prop in enumerate(property_list):
                                prop_value = prop.get('value', '')
                                print(f"    属性集[{m}-{n}]: {prop_value}")
                
            except Exception as e:
                print(f"❌ 提取第{i+1}条商品信息时发生错误: {str(e)}")
                continue
    
    def process_all_pages(self, total_pages):
        """处理所有页面的商品数据"""
        print(f"\n🚀 开始处理所有{total_pages}页商品数据...")
        
        for page_num in range(1, total_pages + 1):
            print(f"\n{'='*50}")
            print(f"正在处理第{page_num}/{total_pages}页")
            print(f"{'='*50}")
            
            # 获取页面数据
            page_data = self.fetch_product_data(page_num)
            
            if page_data:
                # 提取商品信息
                self.extract_product_info(page_data, page_num)
            else:
                print(f"⚠️  第{page_num}页数据获取失败，跳过")
            
            # 添加延时避免请求过快
            if page_num < total_pages:
                print("⏳ 等待2秒后处理下一页...")
                time.sleep(2)
        
        print(f"\n🎉 所有{total_pages}页商品数据处理完成！")
    
    def run(self):
        """运行主程序"""
        print("🚀 商品数据处理器启动")
        print("="*60)
        
        try:
            # 1. 开始监听网络请求
            self.start_listening()
            
            # 2. 打开目标页面
            self.open_target_page()
            
            # 3. 捕获认证信息
            if not self.capture_auth_info():
                print("❌ 未能获取认证信息，程序终止")
                return
            
            # 4. 查询待处理商品总数
            total_count, total_pages = self.query_todo_count()
            
            if total_count == 0:
                print("❌ 未查询到待处理商品，程序终止")
                return
            
            # 5. 处理所有页面数据
            self.process_all_pages(total_pages)
            
        except Exception as e:
            print(f"❌ 程序运行过程中发生错误: {str(e)}")
        
        finally:
            print("\n📋 程序执行完毕")


def main():
    """主函数"""
    print("🎯 商品数据处理器 - 实践代码")
    print("功能：监听网络请求、获取认证信息、处理商品数据")
    print("="*60)
    
    # 创建处理器实例并运行
    processor = ProductDataProcessor()
    processor.run()


if __name__ == "__main__":
    main()
